{"name": "real-scale-solar-system", "version": "1.0.7", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "release": "./scripts/release.sh", "release:patch": "./scripts/release.sh patch", "release:minor": "./scripts/release.sh minor", "release:major": "./scripts/release.sh major", "changelog": "node scripts/extract-changelog.js"}, "dependencies": {"@react-three/drei": "^10.0.7", "@react-three/fiber": "^9.1.2", "@vercel/speed-insights": "^1.2.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.9.2", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "^0.4.4", "react": "^19", "react-dom": "^19", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "^0.176.0"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "@types/three": "^0.176.0", "eslint": "^9.25.1", "eslint-config-next": "15.3.1", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}, "packageManager": "pnpm@10.12.4"}