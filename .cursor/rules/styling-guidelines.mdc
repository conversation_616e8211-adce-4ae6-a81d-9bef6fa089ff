# Styling Guidelines

## 🎨 Tailwind CSS Standards

- **Utility-first**: Use Tailwind utility classes for styling (see [tailwind.config.ts](mdc:tailwind.config.ts))
- **Responsive design**: Use responsive prefixes (`sm:`, `md:`, `lg:`, `xl:`) for mobile-first design
- **Dark mode**: Support dark mode using `dark:` prefix and [next-themes](mdc:package.json)
- **Custom classes**: Extend Tailwind with custom utilities in the config when needed

## 🌌 Space Theme Styling

- **Color palette**: Use space-themed colors (blues, purples, dark backgrounds)
- **Typography**: Use modern, readable fonts for scientific information
- **Animations**: Implement smooth transitions using Framer Motion
- **Glassmorphism**: Use backdrop blur and transparency for UI elements

## 📱 Responsive Design

- **Mobile-first**: Design for mobile devices first, then enhance for larger screens
- **Touch targets**: Ensure interactive elements are at least 44px for touch devices
- **Viewport units**: Use `vh`, `vw` for full-screen layouts
- **Flexbox/Grid**: Use modern CSS layout techniques

## 🎮 UI Component Patterns

- **Modal design**: Follow the pattern in [components/modal/](mdc:components/modal/)
- **Button styles**: Use consistent button styling with hover and focus states
- **Form elements**: Style form inputs with proper focus and error states
- **Loading states**: Implement skeleton loading for better UX

## 🔧 CSS Organization

- **Global styles**: Define global styles in [app/globals.css](mdc:app/globals.css)
- **Component styles**: Use Tailwind classes directly in components
- **Custom animations**: Define keyframes in global CSS when needed
- **CSS variables**: Use CSS custom properties for theme values

## 🎯 Accessibility

- **Color contrast**: Ensure sufficient contrast ratios for text readability
- **Focus indicators**: Provide visible focus indicators for keyboard navigation
- **Screen readers**: Use proper ARIA labels and semantic HTML
- **Motion preferences**: Respect `prefers-reduced-motion` media query

---
