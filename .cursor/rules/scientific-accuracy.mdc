# Scientific Accuracy Guidelines

## 🌌 Astronomical Data Standards

All celestial body data must be scientifically accurate and sourced from reliable astronomical databases.

### Data Sources

- **NASA**: Use NASA's official planetary data for sizes, distances, and orbital parameters
- **IAU**: International Astronomical Union standards for naming and classification
- **Scientific literature**: Peer-reviewed sources for specialized information
- **Real-time data**: Use current astronomical data, not outdated information

### Data Structure

Follow the established structure in [data/planet-types.ts](mdc:data/planet-types.ts) and [data/planet-data.ts](mdc:data/planet-data.ts):

```typescript
interface PlanetData {
  name: string;
  diameter: number; // in kilometers
  distanceFromSun: number; // in AU (Astronomical Units)
  orbitalPeriod: number; // in Earth years
  rotationPeriod: number; // in Earth days
  axialTilt: number; // in degrees
  // ... other scientific properties
}
```

## 🎯 Scale Accuracy

- **True proportions**: Maintain real astronomical proportions for sizes and distances
- **Distance scaling**: Use appropriate scaling factors to make the solar system navigable
- **Size representation**: Planets should appear as small objects compared to the Sun
- **Orbital mechanics**: Implement accurate elliptical orbits with proper eccentricity

## 🔬 Scientific Features

- **Axial tilt**: Include realistic planetary axial tilts affecting rotation visualization
- **Atmospheric effects**: Represent atmospheric characteristics where applicable
- **Surface features**: Use procedurally generated textures based on real planetary data
- **Rings**: Accurately represent Saturn's, Jupiter's, Uranus', and Neptune's ring systems

## 📊 Educational Content

- **Information accuracy**: All displayed scientific information must be factually correct
- **Units**: Use standard astronomical units (AU, km, Earth masses, etc.)
- **Comparisons**: Provide relatable comparisons (e.g., "Jupiter is 318 times more massive than Earth")
- **Updates**: Keep information current with latest astronomical discoveries

## 🎮 Interactive Elements

- **Click information**: Provide accurate, educational content when users click on celestial bodies
- **Real-time data**: Consider integrating real-time astronomical data where possible
- **Educational value**: Ensure all interactions provide educational benefit
- **Scientific context**: Provide context for the vastness of space and scale of celestial objects

## 🔍 Quality Assurance

- **Fact-checking**: Verify all scientific data before implementation
- **Expert review**: Consider having astronomical data reviewed by experts
- **Documentation**: Document sources for all scientific information
- **Updates**: Regularly update data to reflect new astronomical discoveries

---
